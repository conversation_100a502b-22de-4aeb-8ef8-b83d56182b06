<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import {
  useRoute,
  useRouter,
} from 'vue-router'

import { useMenuStore } from '@/utils/menuStore.js'

const route = useRoute()
const router = useRouter()
const menuStore = useMenuStore()

// Sidebar state
const sidebarCollapsed = ref(false)
const isMobile = ref(false)
const sidebarOpen = ref(false)

// User dropdown
const userDropdownOpen = ref(false)

// Check if mobile
const checkMobile = () => {
  isMobile.value = window.innerWidth < 1024
  if (!isMobile.value) {
    sidebarOpen.value = false
  }
}

// Toggle sidebar
const toggleSidebar = () => {
  if (isMobile.value) {
    sidebarOpen.value = !sidebarOpen.value
  } else {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
}

// Navigate to menu item
const navigateToMenu = (item) => {
  if (item.link) {
    menuStore.setActiveMenu(item.text)
    router.push(item.link)
    if (isMobile.value) {
      sidebarOpen.value = false
    }
  } else if (item.children) {
    menuStore.toggleSubmenu(item.text)
  }
}

// Close sidebar on mobile when clicking outside
const closeSidebarOnMobile = () => {
  if (isMobile.value) {
    sidebarOpen.value = false
  }
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)

  // Initialize menu based on current route
  menuStore.initializeFromRoute(route.path)
})
</script>

<template>
  <!-- Import Tailwind CSS via CDN -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- Mobile Overlay -->
    <div v-if="isMobile && sidebarOpen" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
      @click="closeSidebarOnMobile"></div>

    <!-- Sidebar -->
    <aside :class="[
      'fixed top-0 left-0 z-50 h-screen transition-all duration-300 ease-in-out',
      'bg-gradient-to-b from-indigo-900 via-purple-900 to-pink-800',
      'shadow-2xl border-r border-purple-500/20',
      {
        'w-64': !sidebarCollapsed && !isMobile,
        'w-16': sidebarCollapsed && !isMobile,
        'w-64': isMobile && sidebarOpen,
        '-translate-x-full': isMobile && !sidebarOpen,
        'translate-x-0': !isMobile || sidebarOpen
      }
    ]">
      <!-- Logo Section -->
      <div class="flex items-center justify-center h-16 px-4 border-b border-purple-500/20">
        <div v-if="!sidebarCollapsed || isMobile" class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-gradient-to-r from-pink-500 to-violet-500 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 2L3 7v11h4v-6h6v6h4V7l-7-5z" />
            </svg>
          </div>
          <span class="text-xl font-bold text-white">Admin</span>
        </div>
        <div v-else
          class="w-8 h-8 bg-gradient-to-r from-pink-500 to-violet-500 rounded-lg flex items-center justify-center">
          <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 2L3 7v11h4v-6h6v6h4V7l-7-5z" />
          </svg>
        </div>
      </div>

      <!-- Navigation Menu -->
      <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto custom-scrollbar">
        <div v-for="item in menuStore.menuItems" :key="item.text" class="relative">
          <!-- Main Menu Item -->
          <button @click="navigateToMenu(item)" :class="[
            'w-full flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200',
            'hover:bg-white/10 hover:shadow-lg hover:scale-105 group',
            {
              'bg-gradient-to-r from-pink-500/20 to-violet-500/20 text-white shadow-lg border border-pink-500/30': menuStore.isMenuActive(item),
              'text-purple-200 hover:text-white': !menuStore.isMenuActive(item),
              'justify-center': sidebarCollapsed && !isMobile,
              'justify-start': !sidebarCollapsed || isMobile
            }
          ]">
            <i :class="[item.icon, 'text-lg', sidebarCollapsed && !isMobile ? '' : 'mr-3']"></i>
            <span v-if="!sidebarCollapsed || isMobile" class="flex-1 text-left">{{ item.text }}</span>
            <!-- Badge -->
            <span v-if="item.badge && (!sidebarCollapsed || isMobile)"
              class="px-2 py-1 text-xs font-semibold bg-pink-500 text-white rounded-full">
              {{ item.badge }}
            </span>

            <svg v-if="item.children && (!sidebarCollapsed || isMobile)" :class="[
              'w-4 h-4 transition-transform duration-200',
              menuStore.isSubmenuOpen(item.text) ? 'rotate-180' : ''
            ]" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd" />
            </svg>
          </button>

          <!-- Submenu -->
          <div v-if="item.children && (!sidebarCollapsed || isMobile)" :class="[
            'overflow-hidden transition-all duration-300 ease-in-out',
            menuStore.isSubmenuOpen(item.text) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          ]">
            <div class="pl-6 mt-1 space-y-1">
              <button v-for="child in item.children" :key="child.text" @click="navigateToMenu(child)" :class="[
                'w-full flex items-center px-3 py-2 text-sm rounded-md transition-all duration-200 group',
                'hover:bg-white/5 hover:text-white hover:translate-x-1',
                {
                  'bg-pink-500/20 text-white shadow-md': menuStore.isMenuActive(child),
                  'text-purple-300': !menuStore.isMenuActive(child)
                }
              ]">
                <span class="w-2 h-2 bg-purple-400 rounded-full mr-3 transition-all group-hover:bg-pink-400"></span>
                {{ child.text }}
                <!-- Child Badge -->
                <span v-if="child.badge"
                  class="ml-auto px-2 py-1 text-xs font-semibold bg-violet-500 text-white rounded-full">
                  {{ child.badge }}
                </span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- User Profile Section -->
      <div class="border-t border-purple-500/20 p-4">
        <div v-if="!sidebarCollapsed || isMobile" class="flex items-center space-x-3">
          <div
            class="w-10 h-10 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center">
            <span class="text-white font-semibold">A</span>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-white truncate">Admin User</p>
            <p class="text-xs text-purple-300 truncate"><EMAIL></p>
          </div>
        </div>
        <div v-else class="flex justify-center">
          <div
            class="w-10 h-10 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center">
            <span class="text-white font-semibold">A</span>
          </div>
        </div>
      </div>
    </aside>

    <!-- Main Content -->
    <div :class="[
      'transition-all duration-300 ease-in-out',
      {
        'ml-64': !sidebarCollapsed && !isMobile,
        'ml-16': sidebarCollapsed && !isMobile,
        'ml-0': isMobile
      }
    ]">
      <!-- Top Header -->
      <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
        <div class="flex items-center justify-between px-6 py-4">
          <!-- Left side - Menu toggle and breadcrumb -->
          <div class="flex items-center space-x-4">
            <button @click="toggleSidebar"
              class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-2">
                <li>
                  <div class="flex items-center">
                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                    </svg>
                  </div>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="w-5 h-5 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clip-rule="evenodd" />
                    </svg>
                    <span class="text-sm font-medium text-gray-900">{{ menuStore.activeMenu || 'Dashboard' }}</span>
                  </div>
                </li>
              </ol>
            </nav>
          </div>

          <!-- Right side - Search, notifications, user menu -->
          <div class="flex items-center space-x-4">
            <!-- Search -->
            <div class="relative hidden md:block">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input type="text" placeholder="Search..."
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500">
            </div>

            <!-- Notifications -->
            <button
              class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200 relative">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 17h5l-5 5v-5zM10.97 4.97a.75.75 0 0 0-1.08 1.05l-3.99 4.99a.75.75 0 0 0 1.08 1.05l4.01-5.01a.75.75 0 0 0-.02-1.08z" />
              </svg>
              <span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>

            <!-- User Menu -->
            <div class="relative">
              <button @click="userDropdownOpen = !userDropdownOpen"
                class="flex items-center space-x-2 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200">
                <div
                  class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-semibold">A</span>
                </div>
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>

              <!-- User Dropdown -->
              <div v-if="userDropdownOpen"
                class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
                @click.away="userDropdownOpen = false">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                <hr class="my-1">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Content Area -->
      <main class="flex-1 p-6">
        <div class="max-w-7xl mx-auto">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
/* Custom scrollbar for sidebar */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Smooth transitions */
* {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom gradient backgrounds */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* Animation for menu items */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.menu-item {
  animation: slideIn 0.3s ease-out;
}

/* Hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Badge pulse animation */
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

.badge-pulse {
  animation: pulse 2s infinite;
}

/* Notification dot animation */
@keyframes bounce {

  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }

  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }

  70% {
    transform: translate3d(0, -4px, 0);
  }

  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.notification-bounce {
  animation: bounce 1s infinite;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive design helpers */
@media (max-width: 1024px) {
  .sidebar-mobile {
    transform: translateX(-100%);
  }

  .sidebar-mobile.open {
    transform: translateX(0);
  }
}
</style>
