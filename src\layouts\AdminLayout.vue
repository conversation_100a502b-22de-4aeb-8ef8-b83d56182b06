<script setup>
import {
  computed,
  onMounted,
  ref,
} from 'vue'

import {
  useRoute,
  useRouter,
} from 'vue-router'

import { useMenuStore } from '@/utils/menuStore.js'

const route = useRoute()
const router = useRouter()
const menuStore = useMenuStore()

// Sidebar state
const sidebarCollapsed = ref(false)
const isMobile = ref(false)
const sidebarOpen = ref(false)
const userDropdownOpen = ref(false)

// Computed margin for main content
const mainContentMargin = computed(() => {
  if (isMobile.value) {
    return '0px'
  }
  return sidebarCollapsed.value ? '64px' : '208px'
})

// Check if mobile
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
  if (!isMobile.value) {
    sidebarOpen.value = false
  }
}

// Toggle sidebar
const toggleSidebar = () => {
  if (isMobile.value) {
    sidebarOpen.value = !sidebarOpen.value
  } else {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
}

// Navigate to menu item
const navigateToMenu = (item) => {
  if (item.link) {
    menuStore.setActiveMenu(item.text)
    router.push(item.link)
    if (isMobile.value) {
      sidebarOpen.value = false
    }
  } else if (item.children) {
    menuStore.toggleSubmenu(item.text)
  }
}

// Close sidebar on mobile
const closeSidebarOnMobile = () => {
  if (isMobile.value) {
    sidebarOpen.value = false
  }
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
  menuStore.initializeFromRoute(route.path)
})
</script>

<template>
  <!-- Import Tailwind CSS via CDN -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

  <div class="min-h-screen bg-gray-50">
    <!-- Mobile Overlay -->
    <div v-if="isMobile && sidebarOpen" class="fixed inset-0 bg-black bg-opacity-50 z-40" @click="closeSidebarOnMobile">
    </div>

    <!-- Modern Compact Sidebar -->
    <aside :class="[
      'fixed top-0 left-0 z-50 h-screen transition-all duration-300 ease-in-out',
      'bg-white border-r border-gray-200 shadow-lg',
      {
        'w-52': !sidebarCollapsed && !isMobile,
        'w-16': sidebarCollapsed && !isMobile,
        'w-52': isMobile && sidebarOpen,
        '-translate-x-full': isMobile && !sidebarOpen,
        'translate-x-0': !isMobile || sidebarOpen
      }
    ]">
      <!-- Logo Section -->
      <div class="flex items-center justify-between h-14 px-3 border-b border-gray-100">
        <div v-if="!sidebarCollapsed || isMobile" class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div>
            <h1 class="text-lg font-bold text-gray-900">AdminPro</h1>
            <p class="text-xs text-gray-500">v2.0</p>
          </div>
        </div>
        <div v-else class="flex justify-center w-full">
          <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
        </div>

        <!-- Collapse Button (Desktop Only) -->
        <button v-if="!isMobile" @click="toggleSidebar"
          class="p-1.5 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
          :class="{ 'hidden': sidebarCollapsed }">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
          </svg>
        </button>
      </div>

      <!-- Navigation Menu -->
      <nav class="flex-1 px-2 py-3 space-y-1 overflow-y-auto">
        <div v-for="item in menuStore.menuItems" :key="item.text">
          <!-- Main Menu Item -->
          <button @click="navigateToMenu(item)" :class="[
            'w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200',
            'hover:bg-gray-100 group',
            {
              'bg-blue-50 text-blue-700 border-l-4 border-blue-600': menuStore.isMenuActive(item),
              'text-gray-700 hover:text-gray-900': !menuStore.isMenuActive(item),
              'justify-center': sidebarCollapsed && !isMobile,
              'justify-start': !sidebarCollapsed || isMobile
            }
          ]">
            <!-- Icon -->
            <div :class="[
              'flex items-center justify-center w-6 h-6 rounded transition-all duration-200',
              menuStore.isMenuActive(item) ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700',
              sidebarCollapsed && !isMobile ? 'mx-auto' : 'mr-2'
            ]">
              <i :class="[item.icon, 'text-sm']"></i>
            </div>

            <div v-if="!sidebarCollapsed || isMobile" class="flex-1 flex items-center justify-between">
              <span>{{ item.text }}</span>
              <div class="flex items-center space-x-1">
                <!-- Badge -->
                <span v-if="item.badge" class="px-1.5 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                  {{ item.badge }}
                </span>
                <!-- Dropdown arrow -->
                <svg v-if="item.children" :class="[
                  'w-3 h-3 transition-transform duration-200 text-gray-400',
                  menuStore.isSubmenuOpen(item.text) ? 'rotate-180' : ''
                ]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </button>

          <!-- Submenu -->
          <div v-if="item.children && (!sidebarCollapsed || isMobile)" :class="[
            'overflow-hidden transition-all duration-300 ease-in-out',
            menuStore.isSubmenuOpen(item.text) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          ]">
            <div class="ml-4 mt-1 space-y-1 border-l border-gray-200 pl-2">
              <button v-for="child in item.children" :key="child.text" @click="navigateToMenu(child)" :class="[
                'w-full flex items-center px-2 py-1.5 text-sm rounded transition-all duration-200',
                'hover:bg-gray-100',
                {
                  'bg-blue-50 text-blue-700': menuStore.isMenuActive(child),
                  'text-gray-600 hover:text-gray-900': !menuStore.isMenuActive(child)
                }
              ]">
                <div class="w-3 h-3 rounded-full mr-2 flex items-center justify-center">
                  <div class="w-1 h-1 rounded-full bg-current"></div>
                </div>
                <span class="flex-1">{{ child.text }}</span>
                <span v-if="child.badge" class="px-1 py-0.5 text-xs font-medium bg-purple-100 text-purple-800 rounded">
                  {{ child.badge }}
                </span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- User Profile Section -->
      <div class="border-t border-gray-100 p-2">
        <div v-if="!sidebarCollapsed || isMobile" class="flex items-center space-x-2 p-2 rounded-lg bg-gray-50">
          <div
            class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
            <span class="text-white font-semibold text-xs">A</span>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate">Admin User</p>
            <p class="text-xs text-gray-500 truncate"><EMAIL></p>
          </div>
        </div>
        <div v-else class="flex justify-center p-2">
          <div
            class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
            <span class="text-white font-semibold text-xs">A</span>
          </div>
        </div>
      </div>
    </aside>

    <!-- Main Content -->
    <div class="transition-all duration-300 ease-in-out min-h-screen" :style="{
      marginLeft: mainContentMargin
    }">
      <!-- Top Header -->
      <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
        <div class="flex items-center justify-between px-4 py-3">
          <!-- Left side -->
          <div class="flex items-center space-x-3">
            <button @click="toggleSidebar" class="p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            <nav class="flex items-center space-x-1 text-sm">
              <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
              </svg>
              <span class="text-gray-500">/</span>
              <span class="font-medium text-gray-900">{{ menuStore.activeMenu || 'Dashboard' }}</span>
            </nav>
          </div>

          <!-- Right side -->
          <div class="flex items-center space-x-3">
            <!-- Search -->
            <div class="relative hidden md:block">
              <input type="text" placeholder="Search..."
                class="w-64 pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
              <svg class="w-4 h-4 text-gray-400 absolute left-2.5 top-2" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>

            <!-- Notifications -->
            <button class="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md relative">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z" />
              </svg>
              <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>

            <!-- User Menu -->
            <div class="relative">
              <button @click="userDropdownOpen = !userDropdownOpen"
                class="flex items-center space-x-1 p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md">
                <div
                  class="w-6 h-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                  <span class="text-white text-xs font-semibold">A</span>
                </div>
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>

              <!-- User Dropdown -->
              <div v-if="userDropdownOpen"
                class="absolute right-0 mt-1 w-40 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50">
                <a href="#" class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                <a href="#" class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                <hr class="my-1">
                <a href="#" class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Debug Info (Temporary) -->
      <div class="bg-blue-100 border border-blue-200 p-3 m-4 rounded text-sm">
        <strong>🔧 Debug Info:</strong><br>
        Sidebar Collapsed: {{ sidebarCollapsed }}<br>
        Is Mobile: {{ isMobile }}<br>
        Sidebar Open: {{ sidebarOpen }}<br>
        Main Content Margin: {{ mainContentMargin }}<br>
        Window Width: {{ typeof window !== 'undefined' ? window.innerWidth : 'N/A' }}px
      </div>

      <!-- Main Content Area -->
      <main class="p-6 bg-gray-50 min-h-screen">
        <div class="max-w-7xl mx-auto">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
/* Custom scrollbar */
nav::-webkit-scrollbar {
  width: 4px;
}

nav::-webkit-scrollbar-track {
  background: transparent;
}

nav::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
}

nav::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* Smooth transitions */
* {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
