<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import {
  useRoute,
  useRouter,
} from 'vue-router'

import { useMenuStore } from '@/utils/menuStore.js'

const route = useRoute()
const router = useRouter()
const menuStore = useMenuStore()

// Sidebar state
const sidebarCollapsed = ref(false)
const isMobile = ref(false)
const sidebarOpen = ref(false)

// User dropdown
const userDropdownOpen = ref(false)

// Check if mobile
const checkMobile = () => {
  isMobile.value = window.innerWidth < 1024
  if (!isMobile.value) {
    sidebarOpen.value = false
  }
}

// Toggle sidebar
const toggleSidebar = () => {
  if (isMobile.value) {
    sidebarOpen.value = !sidebarOpen.value
  } else {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
}

// Navigate to menu item
const navigateToMenu = (item) => {
  if (item.link) {
    menuStore.setActiveMenu(item.text)
    router.push(item.link)
    if (isMobile.value) {
      sidebarOpen.value = false
    }
  } else if (item.children) {
    menuStore.toggleSubmenu(item.text)
  }
}

// Close sidebar on mobile when clicking outside
const closeSidebarOnMobile = () => {
  if (isMobile.value) {
    sidebarOpen.value = false
  }
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)

  // Initialize menu based on current route
  menuStore.initializeFromRoute(route.path)
})
</script>

<template>
  <!-- Import Tailwind CSS via CDN -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- Mobile Overlay -->
    <div v-if="isMobile && sidebarOpen" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
      @click="closeSidebarOnMobile"></div>

    <!-- Sidebar -->
    <aside :class="[
      'fixed top-0 left-0 z-50 h-screen transition-all duration-300 ease-in-out',
      'bg-white/95 backdrop-blur-xl border-r border-gray-200/50',
      'shadow-xl shadow-gray-900/5',
      {
        'w-56': !sidebarCollapsed && !isMobile,
        'w-14': sidebarCollapsed && !isMobile,
        'w-56': isMobile && sidebarOpen,
        '-translate-x-full': isMobile && !sidebarOpen,
        'translate-x-0': !isMobile || sidebarOpen
      }
    ]">
      <!-- Logo Section -->
      <div class="flex items-center h-16 px-4 border-b border-gray-100/80">
        <div v-if="!sidebarCollapsed || isMobile" class="flex items-center space-x-2">
          <div class="relative">
            <div
              class="w-8 h-8 bg-gradient-to-tr from-blue-600 via-purple-600 to-pink-600 rounded-lg flex items-center justify-center shadow-md">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div class="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-emerald-400 rounded-full border border-white"></div>
          </div>
          <div>
            <h1 class="text-lg font-bold text-gray-900">AdminPro</h1>
            <p class="text-xs text-gray-500">v2.0</p>
          </div>
        </div>
        <div v-else class="flex justify-center w-full">
          <div class="relative">
            <div
              class="w-8 h-8 bg-gradient-to-tr from-blue-600 via-purple-600 to-pink-600 rounded-lg flex items-center justify-center shadow-md">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div class="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-emerald-400 rounded-full border border-white"></div>
          </div>
        </div>
      </div>

      <!-- Navigation Menu -->
      <nav class="flex-1 px-3 py-4 space-y-1 overflow-y-auto custom-scrollbar">
        <div v-for="item in menuStore.menuItems" :key="item.text" class="relative">
          <!-- Main Menu Item -->
          <button @click="navigateToMenu(item)" :class="[
            'w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200',
            'hover:bg-gray-50 group relative',
            {
              'bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 shadow-sm border border-blue-100': menuStore.isMenuActive(item),
              'text-gray-700 hover:text-gray-900': !menuStore.isMenuActive(item),
              'justify-center': sidebarCollapsed && !isMobile,
              'justify-start': !sidebarCollapsed || isMobile
            }
          ]">
            <!-- Active indicator -->
            <div v-if="menuStore.isMenuActive(item)"
              class="absolute left-0 top-1/2 -translate-y-1/2 w-0.5 h-6 bg-gradient-to-b from-blue-500 to-purple-600 rounded-r-full">
            </div>

            <!-- Icon with modern styling -->
            <div :class="[
              'flex items-center justify-center w-7 h-7 rounded-lg transition-all duration-200',
              menuStore.isMenuActive(item) ? 'bg-blue-100 text-blue-600' : 'text-gray-500 group-hover:bg-gray-100 group-hover:text-gray-700',
              sidebarCollapsed && !isMobile ? 'mx-auto' : 'mr-2.5'
            ]">
              <i :class="[item.icon, 'text-sm']"></i>
            </div>

            <div v-if="!sidebarCollapsed || isMobile" class="flex-1 flex items-center justify-between">
              <span class="font-medium text-sm">{{ item.text }}</span>
              <div class="flex items-center space-x-1.5">
                <!-- Badge -->
                <span v-if="item.badge"
                  class="px-2 py-0.5 text-xs font-semibold bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full">
                  {{ item.badge }}
                </span>

                <!-- Dropdown arrow -->
                <svg v-if="item.children" :class="[
                  'w-3.5 h-3.5 transition-transform duration-200 text-gray-400',
                  menuStore.isSubmenuOpen(item.text) ? 'rotate-180' : ''
                ]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </button>

          <!-- Submenu -->
          <div v-if="item.children && (!sidebarCollapsed || isMobile)" :class="[
            'overflow-hidden transition-all duration-300 ease-in-out mt-1',
            menuStore.isSubmenuOpen(item.text) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          ]">
            <div class="ml-5 space-y-0.5 border-l border-gray-200 pl-3">
              <button v-for="child in item.children" :key="child.text" @click="navigateToMenu(child)" :class="[
                'w-full flex items-center px-2.5 py-2 text-sm rounded-md transition-all duration-200 group',
                'hover:bg-gray-50 relative',
                {
                  'bg-blue-50 text-blue-700 border border-blue-100': menuStore.isMenuActive(child),
                  'text-gray-600 hover:text-gray-900': !menuStore.isMenuActive(child)
                }
              ]">
                <!-- Child active indicator -->
                <div v-if="menuStore.isMenuActive(child)"
                  class="absolute left-0 top-1/2 -translate-y-1/2 w-0.5 h-4 bg-blue-500 rounded-r-full"></div>

                <!-- Child icon -->
                <div :class="[
                  'flex items-center justify-center w-4 h-4 rounded mr-2 transition-all duration-200',
                  menuStore.isMenuActive(child) ? 'bg-blue-100 text-blue-600' : 'text-gray-400 group-hover:text-gray-600'
                ]">
                  <div class="w-1 h-1 rounded-full bg-current"></div>
                </div>

                <span class="flex-1 font-medium text-sm">{{ child.text }}</span>

                <!-- Child Badge -->
                <span v-if="child.badge"
                  class="px-1.5 py-0.5 text-xs font-semibold bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full">
                  {{ child.badge }}
                </span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- User Profile Section -->
      <div class="border-t border-gray-100/80 p-3 m-3 rounded-lg bg-gray-50/50">
        <div v-if="!sidebarCollapsed || isMobile" class="flex items-center space-x-2.5">
          <div class="relative">
            <div
              class="w-8 h-8 bg-gradient-to-tr from-blue-600 via-purple-600 to-pink-600 rounded-lg flex items-center justify-center shadow-md">
              <span class="text-white font-semibold text-xs">A</span>
            </div>
            <div class="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-emerald-400 rounded-full border border-white">
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-semibold text-gray-900 truncate">Admin User</p>
            <p class="text-xs text-gray-500 truncate"><EMAIL></p>
            <div class="flex items-center mt-0.5">
              <div class="w-1.5 h-1.5 bg-emerald-400 rounded-full mr-1.5"></div>
              <span class="text-xs text-emerald-600 font-medium">Online</span>
            </div>
          </div>
        </div>
        <div v-else class="flex justify-center">
          <div class="relative">
            <div
              class="w-8 h-8 bg-gradient-to-tr from-blue-600 via-purple-600 to-pink-600 rounded-lg flex items-center justify-center shadow-md">
              <span class="text-white font-semibold text-xs">A</span>
            </div>
            <div class="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-emerald-400 rounded-full border border-white">
            </div>
          </div>
        </div>
      </div>
    </aside>

    <!-- Main Content -->
    <div :class="[
      'transition-all duration-300 ease-in-out min-h-screen',
      {
        'ml-56': !sidebarCollapsed && !isMobile,
        'ml-14': sidebarCollapsed && !isMobile,
        'ml-0': isMobile
      }
    ]">
      <!-- Top Header -->
      <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div class="flex items-center justify-between px-6 py-4">
          <!-- Left side - Menu toggle and breadcrumb -->
          <div class="flex items-center space-x-4">
            <button @click="toggleSidebar"
              class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-2">
                <li>
                  <div class="flex items-center">
                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                    </svg>
                  </div>
                </li>
                <li>
                  <div class="flex items-center">
                    <svg class="w-5 h-5 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clip-rule="evenodd" />
                    </svg>
                    <span class="text-sm font-medium text-gray-900">{{ menuStore.activeMenu || 'Dashboard' }}</span>
                  </div>
                </li>
              </ol>
            </nav>
          </div>

          <!-- Right side - Search, notifications, user menu -->
          <div class="flex items-center space-x-4">
            <!-- Search -->
            <div class="relative hidden md:block">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input type="text" placeholder="Search..."
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500">
            </div>

            <!-- Notifications -->
            <button
              class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200 relative">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 17h5l-5 5v-5zM10.97 4.97a.75.75 0 0 0-1.08 1.05l-3.99 4.99a.75.75 0 0 0 1.08 1.05l4.01-5.01a.75.75 0 0 0-.02-1.08z" />
              </svg>
              <span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>

            <!-- User Menu -->
            <div class="relative">
              <button @click="userDropdownOpen = !userDropdownOpen"
                class="flex items-center space-x-2 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200">
                <div
                  class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-semibold">A</span>
                </div>
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>

              <!-- User Dropdown -->
              <div v-if="userDropdownOpen"
                class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
                @click.away="userDropdownOpen = false">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                <hr class="my-1">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Content Area -->
      <main class="flex-1 p-6 bg-gray-50">
        <div class="max-w-7xl mx-auto">
          <!-- Debug info -->
          <div class="mb-4 p-3 bg-blue-100 border border-blue-200 rounded-lg text-sm text-blue-800">
            <strong>Layout Debug:</strong>
            Sidebar: {{ !sidebarCollapsed && !isMobile ? '256px (ml-64)' : sidebarCollapsed && !isMobile ? '64px (ml-16)' : '0px (ml-0)' }} |
            Mobile: {{ isMobile ? 'Yes' : 'No' }} |
            Collapsed: {{ sidebarCollapsed ? 'Yes' : 'No' }}
          </div>
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
/* Custom scrollbar for sidebar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* Smooth transitions */
* {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom gradient backgrounds */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* Animation for menu items */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.menu-item {
  animation: slideIn 0.3s ease-out;
}

/* Hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Badge pulse animation */
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

.badge-pulse {
  animation: pulse 2s infinite;
}

/* Notification dot animation */
@keyframes bounce {

  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }

  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }

  70% {
    transform: translate3d(0, -4px, 0);
  }

  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.notification-bounce {
  animation: bounce 1s infinite;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive design helpers */
@media (max-width: 1024px) {
  .sidebar-mobile {
    transform: translateX(-100%);
  }

  .sidebar-mobile.open {
    transform: translateX(0);
  }
}
</style>
