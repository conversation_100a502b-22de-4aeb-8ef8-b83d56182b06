<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import useSocialMedia from '@/stores/socialMedia'

const letterData = ref(null);
onMounted(async () => {
    letterData.value = await useSocialMedia().getAllnewsLetter();
})
</script>
<template>
    <div class="support-ticket-system support-ticket-system--search">

        <div class="breadcrumb-main m-0 breadcrumb-main--table justify-content-sm-between ">
            <div class=" d-flex flex-wrap justify-content-center breadcrumb-main__wrapper">
                <div class="d-flex align-items-center ticket__title justify-content-center me-md-25 mb-md-0 mb-20">
                    <h4 class="text-capitalize fw-500 breadcrumb-title">News Letter Lists</h4>
                </div>
            </div>
            <div class="action-btn">
                <a href="#" class="btn btn-primary">
                    Export
                    <i class="las la-angle-down"></i>
                </a>
            </div>
        </div>


        <div class="userDatatable userDatatable--ticket userDatatable--ticket--2 mt-1">
            <div class="table-responsive">
                <table class="table mb-0 table-borderless">
                    <thead>
                        <tr class="userDatatable-header">
                            <th class="pe-0">
                                <div class="d-flex align-items-center">
                                    <div class="custom-checkbox  check-all">
                                        <input class="checkbox" type="checkbox" id="check-333">
                                        <label for="check-333" class="ps-0">
                                            <span class="checkbox-text userDatatable-title"></span>
                                        </label>
                                    </div>
                                </div>
                            </th>
                            <th>
                                <span class="userDatatable-title">Email</span>
                            </th>
                            <th>
                                <span class="userDatatable-title">Created_At</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <div v-if="!letterData" class="dm-spin-dots spin-lg">
                            <span class="spin-dot badge-dot dot-primary"></span>
                            <span class="spin-dot badge-dot dot-primary"></span>
                            <span class="spin-dot badge-dot dot-primary"></span>
                            <span class="spin-dot badge-dot dot-primary"></span>
                        </div>
                        <tr v-for="letter in letterData" :key="letter.id">
                            <td class="pe-0">
                                <div class="d-flex">
                                    <div class="userDatatable__imgWrapper d-flex align-items-center m-0">
                                        <div class="checkbox-group-wrapper">
                                            <div class="checkbox-group d-flex">
                                                <div
                                                    class="checkbox-theme-default custom-checkbox checkbox-group__single d-flex">
                                                    <input class="checkbox" type="checkbox"
                                                        :id="'check-grp-' + letter.id">
                                                    <label :for="'check-grp-' + letter.id" class="ps-0"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex">
                                    <div class="">
                                        <a href="#" class="text-dark fw-500">
                                            <h6>{{ letter.email }}</h6>
                                        </a>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="userDatatable-content--subject">
                                    {{ letter.created_at }}
                                </div>
                            </td>

                        </tr>


                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-end pt-30">

                <nav class="dm-page ">
                    <ul class="dm-pagination d-flex">
                        <li class="dm-pagination__item">
                            <a href="#" class="dm-pagination__link pagination-control"><span
                                    class="la la-angle-left"></span></a>
                            <a href="#" class="dm-pagination__link"><span class="page-number">1</span></a>
                            <a href="#" class="dm-pagination__link active"><span class="page-number">2</span></a>
                            <a href="#" class="dm-pagination__link"><span class="page-number">3</span></a>
                            <a href="#" class="dm-pagination__link pagination-control"><span
                                    class="page-number">...</span></a>
                            <a href="#" class="dm-pagination__link"><span class="page-number">12</span></a>
                            <a href="#" class="dm-pagination__link pagination-control"><span
                                    class="la la-angle-right"></span></a>
                            <a href="#" class="dm-pagination__option">
                            </a>
                        </li>
                        <li class="dm-pagination__item">
                            <div class="paging-option">
                                <select name="page-number" class="page-selection">
                                    <option value="20">20/page</option>
                                    <option value="40">40/page</option>
                                    <option value="60">60/page</option>
                                </select>
                            </div>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</template>
<style scoped>
.support-ticket-system {
    margin-top: 20px;
}   
</style>